import React, { useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { debugWebShareAPI, testNativeSharing } from '../utils/shareUtils';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  shareData: {
    title: string;
    text: string;
    url?: string;
    filename?: string;
  };
  onShareOption: (option: string) => void;
}

const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose, shareData, onShareOption }) => {
  const { isDark } = useTheme();

  // Debug Web Share API when modal opens
  useEffect(() => {
    if (isOpen) {
      debugWebShareAPI();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const shareOptions = [
    {
      id: 'native',
      name: 'System Share',
      icon: '📤',
      description: navigator.share ? 'Use device\'s native sharing' : 'Not available on this browser',
      available: !!(navigator.share && window.isSecureContext)
    },
    {
      id: 'email',
      name: 'Email',
      icon: '📧',
      description: 'Share via email',
      available: true
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: '💬',
      description: 'Share to WhatsApp',
      available: true
    },
    {
      id: 'telegram',
      name: 'Telegram',
      icon: '✈️',
      description: 'Share to Telegram',
      available: true
    },
    {
      id: 'twitter',
      name: 'Twitter/X',
      icon: '🐦',
      description: 'Share to Twitter',
      available: true
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: '📘',
      description: 'Share to Facebook',
      available: true
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      icon: '💼',
      description: 'Share to LinkedIn',
      available: true
    },

    {
      id: 'download',
      name: 'Download',
      icon: '⬇️',
      description: 'Download file',
      available: !!shareData.url
    }
  ];

  const availableOptions = shareOptions.filter(option => option.available);

  const handleOptionClick = async (optionId: string) => {
    console.log('ShareModal handleOptionClick called with:', optionId);

    // For native sharing, we need to call navigator.share immediately
    // to preserve the user gesture context
    if (optionId === 'native') {
      console.log('Native share option clicked');
      console.log('navigator.share available:', !!navigator.share);

      if (!navigator.share) {
        console.log('navigator.share not available, falling back');
        alert('Native sharing is not available in this browser. Please try another sharing option.');
        return;
      }

      try {
        console.log('Direct native share attempt from modal');
        console.log('shareData received:', shareData);

        // Prepare share data - be more conservative with what we include
        const nativeShareData: any = {};

        // Always include title
        if (shareData.title) {
          nativeShareData.title = shareData.title;
        }

        // Always include text - this is the most compatible option
        if (shareData.text) {
          nativeShareData.text = shareData.text;
        }

        // Only add URL if it's a valid HTTP/HTTPS URL (not blob URLs)
        if (shareData.url &&
            (shareData.url.startsWith('http://') || shareData.url.startsWith('https://')) &&
            !shareData.url.startsWith('blob:')) {
          nativeShareData.url = shareData.url;
        }

        console.log('Prepared native share data:', nativeShareData);

        // Validate the data before sharing
        if (navigator.canShare) {
          const canShare = navigator.canShare(nativeShareData);
          console.log('Can share check result:', canShare);

          if (!canShare) {
            console.log('Data cannot be shared, trying text-only');
            // Try with just title and text
            const textOnlyData = {
              title: nativeShareData.title,
              text: nativeShareData.text
            };

            if (navigator.canShare(textOnlyData)) {
              console.log('Using text-only share data');
              Object.assign(nativeShareData, textOnlyData);
            } else {
              throw new Error('Share data is not supported by this browser');
            }
          }
        }

        console.log('About to call navigator.share...');
        console.log('User activation state:', navigator.userActivation ? {
          hasBeenActive: navigator.userActivation.hasBeenActive,
          isActive: navigator.userActivation.isActive
        } : 'not available');

        // Call navigator.share directly here to preserve user gesture
        await navigator.share(nativeShareData);

        console.log('Native share completed successfully');
        onClose(); // Close modal on success
        return;
      } catch (error: any) {
        console.error('Native share failed with error:', error);
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);

        if (error.name === 'AbortError') {
          console.log('User cancelled native share');
          // Don't close modal if user cancelled - let them try again
          return;
        } else if (error.name === 'NotAllowedError') {
          console.log('Native share not allowed - user gesture required');
          alert('Native sharing requires a direct user action. Please try clicking the button again.');
          return;
        } else {
          // For other errors, show error but don't close modal
          console.log('Native share error:', error.message);
          alert(`Native sharing failed: ${error.message}. Please try another sharing option.`);
          return;
        }
      }
    }

    // For all other options, use the parent handler
    console.log('Using parent handler for option:', optionId);
    onShareOption(optionId);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
        padding: '20px'
      }}
      onClick={handleBackdropClick}
    >
      <div
        style={{
          backgroundColor: isDark ? '#1f2937' : '#ffffff',
          borderRadius: '16px',
          padding: '24px',
          maxWidth: '400px',
          width: '100%',
          maxHeight: '80vh',
          overflowY: 'auto',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div style={{ marginBottom: '20px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <h3 style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: isDark ? '#f9fafb' : '#111827'
            }}>
              Share Invoice
            </h3>
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: isDark ? '#9ca3af' : '#6b7280',
                padding: '4px',
                borderRadius: '4px'
              }}
            >
              ×
            </button>
          </div>
          <p style={{
            margin: 0,
            fontSize: '14px',
            color: isDark ? '#9ca3af' : '#6b7280'
          }}>
            {shareData.title}
          </p>
        </div>

        {/* Debug Test Button - Remove this in production */}
        {navigator.share && (
          <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: isDark ? '#374151' : '#f3f4f6', borderRadius: '8px' }}>
            <p style={{ margin: '0 0 10px 0', fontSize: '12px', color: isDark ? '#9ca3af' : '#6b7280' }}>
              Debug: Test native sharing
            </p>
            <button
              onClick={async () => {
                try {
                  const success = await testNativeSharing();
                  alert(success ? 'Test share successful!' : 'Test share failed - check console for details');
                } catch (error: any) {
                  alert(`Test error: ${error.message}`);
                }
              }}
              style={{
                padding: '8px 16px',
                backgroundColor: isDark ? '#4b5563' : '#e5e7eb',
                border: 'none',
                borderRadius: '6px',
                fontSize: '12px',
                cursor: 'pointer',
                color: isDark ? '#f9fafb' : '#111827'
              }}
            >
              Test Native Share
            </button>
          </div>
        )}

        {/* Share Options Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '12px',
          marginBottom: '20px'
        }}>
          {availableOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => handleOptionClick(option.id)}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                padding: '16px 12px',
                border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
                borderRadius: '12px',
                backgroundColor: isDark ? '#374151' : '#f9fafb',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                textAlign: 'center'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = isDark ? '#4b5563' : '#f3f4f6';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = isDark ? '#374151' : '#f9fafb';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                {option.icon}
              </div>
              <div style={{
                fontSize: '12px',
                fontWeight: '500',
                color: isDark ? '#f9fafb' : '#111827',
                marginBottom: '4px'
              }}>
                {option.name}
              </div>
              <div style={{
                fontSize: '10px',
                color: isDark ? '#9ca3af' : '#6b7280',
                lineHeight: '1.2'
              }}>
                {option.description}
              </div>
            </button>
          ))}
        </div>

        {/* Cancel Button */}
        <button
          onClick={onClose}
          style={{
            width: '100%',
            padding: '12px',
            border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
            borderRadius: '8px',
            backgroundColor: 'transparent',
            color: isDark ? '#f9fafb' : '#374151',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ShareModal;
